package com.logictrue.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.logictrue.config.ConfigManager;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 网络服务类，处理HTTP请求和文件下载
 */
public class NetworkService {
    private static final Logger logger = LoggerFactory.getLogger(NetworkService.class);
    private static final String CACHE_DIR = System.getProperty("user.home") + "/.iot-jfx/cache";
    
    private CloseableHttpClient httpClient;
    private ObjectMapper objectMapper;
    private ConfigManager configManager;
    
    public NetworkService() {
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(5000)
                .setSocketTimeout(5000)
                .build();
        
        this.httpClient = HttpClients.custom()
                .setDefaultRequestConfig(config)
                .build();
        this.objectMapper = new ObjectMapper();
        this.configManager = ConfigManager.getInstance();
        
        // 创建缓存目录
        try {
            Files.createDirectories(Paths.get(CACHE_DIR));
        } catch (IOException e) {
            logger.error("创建缓存目录失败", e);
        }
    }
    
    /**
     * 异步下载设备图片
     */
    public CompletableFuture<String> downloadDeviceImage(String deviceId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String imageUrl = configManager.getImageUrl() + "?deviceId=" + deviceId;
                String fileName = "device_" + deviceId + ".jpg";
                String localPath = CACHE_DIR + File.separator + fileName;
                
                logger.info("开始下载设备图片: {}", imageUrl);
                
                HttpGet httpGet = new HttpGet(imageUrl);
                HttpResponse response = httpClient.execute(httpGet);
                
                if (response.getStatusLine().getStatusCode() == 200) {
                    try (InputStream inputStream = response.getEntity().getContent();
                         FileOutputStream outputStream = new FileOutputStream(localPath)) {
                        
                        byte[] buffer = new byte[4096];
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            outputStream.write(buffer, 0, bytesRead);
                        }
                    }
                    
                    logger.info("设备图片下载成功: {}", localPath);
                    return localPath;
                } else {
                    logger.error("下载设备图片失败，HTTP状态码: {}", response.getStatusLine().getStatusCode());
                    return null;
                }
            } catch (Exception e) {
                logger.error("下载设备图片异常", e);
                return null;
            }
        });
    }
    
    /**
     * 异步发送心跳请求
     */
    public CompletableFuture<HeartbeatResult> sendHeartbeat() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                String heartbeatUrl = configManager.getHeartbeatUrl();
                HttpGet httpGet = new HttpGet(heartbeatUrl);
                
                HttpResponse response = httpClient.execute(httpGet);
                long responseTime = System.currentTimeMillis() - startTime;
                
                int statusCode = response.getStatusLine().getStatusCode();
                String responseBody = EntityUtils.toString(response.getEntity());
                
                boolean success = statusCode >= 200 && statusCode < 300;
                
                return new HeartbeatResult(success, statusCode, responseTime, responseBody);
            } catch (Exception e) {
                long responseTime = System.currentTimeMillis() - startTime;
                logger.error("心跳请求异常", e);
                return new HeartbeatResult(false, -1, responseTime, e.getMessage());
            }
        });
    }
    
    /**
     * 异步提交表单数据
     */
    public CompletableFuture<Boolean> submitForm(Map<String, Object> formData) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String apiUrl = configManager.getApiUrl();
                HttpPost httpPost = new HttpPost(apiUrl);
                httpPost.setHeader("Content-Type", "application/json");
                
                String jsonData = objectMapper.writeValueAsString(formData);
                httpPost.setEntity(new StringEntity(jsonData, "UTF-8"));
                
                HttpResponse response = httpClient.execute(httpPost);
                int statusCode = response.getStatusLine().getStatusCode();
                
                boolean success = statusCode >= 200 && statusCode < 300;
                logger.info("表单提交结果: {}, 状态码: {}", success ? "成功" : "失败", statusCode);
                
                return success;
            } catch (Exception e) {
                logger.error("表单提交异常", e);
                return false;
            }
        });
    }
    
    /**
     * 关闭HTTP客户端
     */
    public void close() {
        try {
            if (httpClient != null) {
                httpClient.close();
            }
        } catch (IOException e) {
            logger.error("关闭HTTP客户端异常", e);
        }
    }
    
    /**
     * 心跳结果数据类
     */
    public static class HeartbeatResult {
        private final boolean success;
        private final int statusCode;
        private final long responseTime;
        private final String message;
        
        public HeartbeatResult(boolean success, int statusCode, long responseTime, String message) {
            this.success = success;
            this.statusCode = statusCode;
            this.responseTime = responseTime;
            this.message = message;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public int getStatusCode() {
            return statusCode;
        }
        
        public long getResponseTime() {
            return responseTime;
        }
        
        public String getMessage() {
            return message;
        }
    }
}
