package com.logictrue;

import com.logictrue.controller.MainController;
import javafx.application.Application;
import javafx.application.Platform;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * IoT数据采集JavaFX应用程序
 */
public class App extends Application {
    private static final Logger logger = LoggerFactory.getLogger(App.class);

    private MainController mainController;

    @Override
    public void start(Stage primaryStage) {
        try {
            logger.info("启动IoT数据采集应用程序");

            // 加载主界面FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/main.fxml"));
            Parent root = loader.load();

            // 获取控制器引用
            mainController = loader.getController();

            // 创建场景并应用样式
            Scene scene = new Scene(root, 800, 600);
            scene.getStylesheets().add(getClass().getResource("/css/application.css").toExternalForm());

            // 配置主窗口
            primaryStage.setTitle("IoT数据采集系统");
            primaryStage.setScene(scene);
            primaryStage.setMinWidth(800);
            primaryStage.setMinHeight(600);
            primaryStage.setResizable(true);

            // 设置关闭事件处理
            primaryStage.setOnCloseRequest(event -> {
                logger.info("应用程序正在关闭");
                if (mainController != null) {
                    mainController.shutdown();
                }
                Platform.exit();
                System.exit(0);
            });

            primaryStage.show();
            logger.info("应用程序启动成功");

        } catch (Exception e) {
            logger.error("启动应用程序失败", e);
            Platform.exit();
            System.exit(1);
        }
    }

    public static void main(String[] args) {
        launch(args);
    }
}
