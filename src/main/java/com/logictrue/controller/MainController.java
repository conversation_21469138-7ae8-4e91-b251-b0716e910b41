package com.logictrue.controller;

import com.logictrue.config.ConfigManager;
import com.logictrue.model.ExternalApp;
import com.logictrue.service.ExternalAppService;
import com.logictrue.service.HeartbeatService;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;
import javafx.scene.shape.SVGPath;
import javafx.stage.Modality;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;

/**
 * 主界面控制器
 */
public class MainController implements Initializable {
    private static final Logger logger = LoggerFactory.getLogger(MainController.class);
    
    @FXML
    private StackPane backgroundPane;
    
    @FXML
    private ImageView backgroundImageView;
    
    @FXML
    private Button settingsButton;
    
    @FXML
    private Button quickStartButton;

    @FXML
    private HBox externalAppsBox;

    private ConfigManager configManager;
    private HeartbeatService heartbeatService;
    private ExternalAppService externalAppService;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        configManager = ConfigManager.getInstance();
        heartbeatService = new HeartbeatService();
        externalAppService = new ExternalAppService();

        // 初始化界面
        initializeUI();

        // 启动心跳服务
        heartbeatService.start();

        // 加载背景图片
        loadBackgroundImage();

        // 加载外部应用程序按钮
        loadExternalAppButtons();

        logger.info("主界面初始化完成");
    }
    
    /**
     * 初始化UI组件
     */
    private void initializeUI() {
        // 设置快速开始按钮样式
        quickStartButton.getStyleClass().add("quick-start-button");

        // 设置设置按钮样式和SVG图标
        settingsButton.getStyleClass().add("settings-button");
        setupSettingsButtonIcon();

        // 绑定事件
        settingsButton.setOnAction(event -> openSettingsWindow());
        quickStartButton.setOnAction(event -> openFormWindow());
    }

    /**
     * 设置设置按钮的SVG图标
     */
    private void setupSettingsButtonIcon() {
        // 创建SVG路径 - 简洁的设置齿轮图标
        SVGPath settingsIcon = new SVGPath();
        settingsIcon.setContent("M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z");
        settingsIcon.setFill(javafx.scene.paint.Color.web("#666666"));

        // 清除按钮文本并设置图标
        settingsButton.setText("");
        settingsButton.setGraphic(settingsIcon);
    }
    
    /**
     * 加载背景图片
     */
    private void loadBackgroundImage() {
        String backgroundImagePath = configManager.getBackgroundImagePath();
        
        if (backgroundImagePath != null && !backgroundImagePath.isEmpty()) {
            File imageFile = new File(backgroundImagePath);
            if (imageFile.exists()) {
                try {
                    Image backgroundImage = new Image(imageFile.toURI().toString());
                    backgroundImageView.setImage(backgroundImage);
                    logger.info("加载背景图片成功: {}", backgroundImagePath);
                } catch (Exception e) {
                    logger.error("加载背景图片失败: {}", backgroundImagePath, e);
                    loadDefaultBackground();
                }
            } else {
                logger.warn("背景图片文件不存在: {}", backgroundImagePath);
                loadDefaultBackground();
            }
        } else {
            loadDefaultBackground();
        }
    }
    
    /**
     * 加载默认背景图片
     */
    private void loadDefaultBackground() {
        try {
            // 尝试加载默认背景图片
            URL defaultImageUrl = getClass().getResource("/images/default-background.jpg");
            if (defaultImageUrl != null) {
                Image defaultImage = new Image(defaultImageUrl.toString());
                backgroundImageView.setImage(defaultImage);
                logger.info("加载默认背景图片成功");
            } else {
                logger.warn("默认背景图片不存在");
            }
        } catch (Exception e) {
            logger.error("加载默认背景图片失败", e);
        }
    }
    
    /**
     * 打开设置窗口
     */
    @FXML
    private void openSettingsWindow() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/settings.fxml"));
            Parent root = loader.load();
            
            SettingsController settingsController = loader.getController();
            settingsController.setMainController(this);
            
            Stage settingsStage = new Stage();
            settingsStage.setTitle("设置");
            settingsStage.setScene(new Scene(root, 700, 600));
            settingsStage.initModality(Modality.APPLICATION_MODAL);
            settingsStage.setResizable(true);
            
            settingsStage.showAndWait();
        } catch (IOException e) {
            logger.error("打开设置窗口失败", e);
        }
    }
    
    /**
     * 打开表单窗口
     */
    @FXML
    private void openFormWindow() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/dynamic-form.fxml"));
            Parent root = loader.load();

            DynamicFormController formController = loader.getController();
            formController.setMainController(this);

            Stage formStage = new Stage();
            formStage.setTitle(configManager.getFormName());
            formStage.setScene(new Scene(root, 600, 500));
            formStage.initModality(Modality.APPLICATION_MODAL);
            formStage.setResizable(true);

            formStage.showAndWait();
        } catch (IOException e) {
            logger.error("打开表单窗口失败", e);
        }
    }
    
    /**
     * 更新背景图片
     */
    public void updateBackgroundImage(String imagePath) {
        Platform.runLater(() -> {
            if (imagePath != null && !imagePath.isEmpty()) {
                File imageFile = new File(imagePath);
                if (imageFile.exists()) {
                    try {
                        Image newImage = new Image(imageFile.toURI().toString());
                        backgroundImageView.setImage(newImage);
                        logger.info("背景图片更新成功: {}", imagePath);
                    } catch (Exception e) {
                        logger.error("更新背景图片失败: {}", imagePath, e);
                    }
                }
            }
        });
    }
    
    /**
     * 获取心跳服务
     */
    public HeartbeatService getHeartbeatService() {
        return heartbeatService;
    }
    
    /**
     * 加载外部应用程序按钮
     */
    private void loadExternalAppButtons() {
        List<ExternalApp> externalApps = configManager.getExternalApps();
        externalAppsBox.getChildren().clear();

        for (ExternalApp app : externalApps) {
            Button appButton = new Button(app.getName());
            appButton.getStyleClass().add("external-app-button");
            appButton.setOnAction(event -> launchExternalApp(app));
            externalAppsBox.getChildren().add(appButton);
        }

        logger.info("加载外部应用程序按钮完成，共{}个应用", externalApps.size());
    }

    /**
     * 启动外部应用程序
     */
    private void launchExternalApp(ExternalApp app) {
        logger.info("启动外部应用程序: {}", app.getName());

        externalAppService.launchApp(app).thenAccept(success -> {
            Platform.runLater(() -> {
                if (success) {
                    logger.info("外部应用程序启动成功: {}", app.getName());
                } else {
                    logger.error("外部应用程序启动失败: {}", app.getName());
                    // 可以在这里添加错误提示对话框
                }
            });
        });
    }

    /**
     * 刷新外部应用程序按钮（在设置更新后调用）
     */
    public void refreshExternalAppButtons() {
        Platform.runLater(this::loadExternalAppButtons);
    }

    /**
     * 应用关闭时的清理工作
     */
    public void shutdown() {
        if (heartbeatService != null) {
            heartbeatService.stop();
        }
        logger.info("主界面控制器关闭");
    }
}
