package com.logictrue.controller;

import com.logictrue.config.ConfigManager;
import com.logictrue.model.ExternalApp;
import com.logictrue.service.ExternalAppService;
import com.logictrue.service.HeartbeatService;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;
import javafx.stage.Modality;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;

/**
 * 主界面控制器
 */
public class MainController implements Initializable {
    private static final Logger logger = LoggerFactory.getLogger(MainController.class);
    
    @FXML
    private StackPane backgroundPane;
    
    @FXML
    private ImageView backgroundImageView;
    
    @FXML
    private Button settingsButton;
    
    @FXML
    private Button quickStartButton;

    @FXML
    private HBox externalAppsBox;

    private ConfigManager configManager;
    private HeartbeatService heartbeatService;
    private ExternalAppService externalAppService;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        configManager = ConfigManager.getInstance();
        heartbeatService = new HeartbeatService();
        externalAppService = new ExternalAppService();

        // 初始化界面
        initializeUI();

        // 启动心跳服务
        heartbeatService.start();

        // 加载背景图片
        loadBackgroundImage();

        // 加载外部应用程序按钮
        loadExternalAppButtons();

        logger.info("主界面初始化完成");
    }
    
    /**
     * 初始化UI组件
     */
    private void initializeUI() {
        // 设置快速开始按钮样式
        quickStartButton.getStyleClass().add("quick-start-button");
        
        // 设置设置按钮样式
        settingsButton.getStyleClass().add("settings-button");
        
        // 绑定事件
        settingsButton.setOnAction(event -> openSettingsWindow());
        quickStartButton.setOnAction(event -> openFormWindow());
    }
    
    /**
     * 加载背景图片
     */
    private void loadBackgroundImage() {
        String backgroundImagePath = configManager.getBackgroundImagePath();
        
        if (backgroundImagePath != null && !backgroundImagePath.isEmpty()) {
            File imageFile = new File(backgroundImagePath);
            if (imageFile.exists()) {
                try {
                    Image backgroundImage = new Image(imageFile.toURI().toString());
                    backgroundImageView.setImage(backgroundImage);
                    logger.info("加载背景图片成功: {}", backgroundImagePath);
                } catch (Exception e) {
                    logger.error("加载背景图片失败: {}", backgroundImagePath, e);
                    loadDefaultBackground();
                }
            } else {
                logger.warn("背景图片文件不存在: {}", backgroundImagePath);
                loadDefaultBackground();
            }
        } else {
            loadDefaultBackground();
        }
    }
    
    /**
     * 加载默认背景图片
     */
    private void loadDefaultBackground() {
        try {
            // 尝试加载默认背景图片
            URL defaultImageUrl = getClass().getResource("/images/default-background.jpg");
            if (defaultImageUrl != null) {
                Image defaultImage = new Image(defaultImageUrl.toString());
                backgroundImageView.setImage(defaultImage);
                logger.info("加载默认背景图片成功");
            } else {
                logger.warn("默认背景图片不存在");
            }
        } catch (Exception e) {
            logger.error("加载默认背景图片失败", e);
        }
    }
    
    /**
     * 打开设置窗口
     */
    @FXML
    private void openSettingsWindow() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/settings.fxml"));
            Parent root = loader.load();
            
            SettingsController settingsController = loader.getController();
            settingsController.setMainController(this);
            
            Stage settingsStage = new Stage();
            settingsStage.setTitle("设置");
            settingsStage.setScene(new Scene(root, 700, 600));
            settingsStage.initModality(Modality.APPLICATION_MODAL);
            settingsStage.setResizable(true);
            
            settingsStage.showAndWait();
        } catch (IOException e) {
            logger.error("打开设置窗口失败", e);
        }
    }
    
    /**
     * 打开表单窗口
     */
    @FXML
    private void openFormWindow() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/dynamic-form.fxml"));
            Parent root = loader.load();

            DynamicFormController formController = loader.getController();
            formController.setMainController(this);

            Stage formStage = new Stage();
            formStage.setTitle(configManager.getFormName());
            formStage.setScene(new Scene(root, 600, 500));
            formStage.initModality(Modality.APPLICATION_MODAL);
            formStage.setResizable(true);

            formStage.showAndWait();
        } catch (IOException e) {
            logger.error("打开表单窗口失败", e);
        }
    }
    
    /**
     * 更新背景图片
     */
    public void updateBackgroundImage(String imagePath) {
        Platform.runLater(() -> {
            if (imagePath != null && !imagePath.isEmpty()) {
                File imageFile = new File(imagePath);
                if (imageFile.exists()) {
                    try {
                        Image newImage = new Image(imageFile.toURI().toString());
                        backgroundImageView.setImage(newImage);
                        logger.info("背景图片更新成功: {}", imagePath);
                    } catch (Exception e) {
                        logger.error("更新背景图片失败: {}", imagePath, e);
                    }
                }
            }
        });
    }
    
    /**
     * 获取心跳服务
     */
    public HeartbeatService getHeartbeatService() {
        return heartbeatService;
    }
    
    /**
     * 加载外部应用程序按钮
     */
    private void loadExternalAppButtons() {
        List<ExternalApp> externalApps = configManager.getExternalApps();
        externalAppsBox.getChildren().clear();

        for (ExternalApp app : externalApps) {
            Button appButton = new Button(app.getName());
            appButton.getStyleClass().add("external-app-button");
            appButton.setOnAction(event -> launchExternalApp(app));
            externalAppsBox.getChildren().add(appButton);
        }

        logger.info("加载外部应用程序按钮完成，共{}个应用", externalApps.size());
    }

    /**
     * 启动外部应用程序
     */
    private void launchExternalApp(ExternalApp app) {
        logger.info("启动外部应用程序: {}", app.getName());

        externalAppService.launchApp(app).thenAccept(success -> {
            Platform.runLater(() -> {
                if (success) {
                    logger.info("外部应用程序启动成功: {}", app.getName());
                } else {
                    logger.error("外部应用程序启动失败: {}", app.getName());
                    // 可以在这里添加错误提示对话框
                }
            });
        });
    }

    /**
     * 刷新外部应用程序按钮（在设置更新后调用）
     */
    public void refreshExternalAppButtons() {
        Platform.runLater(this::loadExternalAppButtons);
    }

    /**
     * 应用关闭时的清理工作
     */
    public void shutdown() {
        if (heartbeatService != null) {
            heartbeatService.stop();
        }
        logger.info("主界面控制器关闭");
    }
}
