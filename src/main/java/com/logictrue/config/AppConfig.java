package com.logictrue.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.logictrue.model.ExternalApp;
import com.logictrue.model.FormField;

import java.util.ArrayList;
import java.util.List;

/**
 * 应用配置数据类
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppConfig {

    private String deviceId = "";
    private String formName = "数据采集表单";
    private String apiUrl = "http://localhost:8080/api/submit";
    private String heartbeatUrl = "http://localhost:8080/api/heartbeat";
    private String imageUrl = "http://localhost:8080/api/device/image";
    private String backgroundImagePath = "";
    private List<FormField> formFields = new ArrayList<>();
    private List<ExternalApp> externalApps = new ArrayList<>();

    public AppConfig() {
        initializeDefaultFormFields();
    }

    /**
     * 初始化默认表单字段
     */
    private void initializeDefaultFormFields() {
        if (formFields.isEmpty()) {
            // 添加默认的表单字段
            FormField deviceIdField = new FormField();
            deviceIdField.setId("deviceId");
            deviceIdField.setLabel("设备编号");
            deviceIdField.setName("deviceId");
            deviceIdField.setType(FormField.FieldType.TEXT);
            formFields.add(deviceIdField);

            FormField operatorField = new FormField();
            operatorField.setId("operator");
            operatorField.setLabel("操作员");
            operatorField.setName("operator");
            operatorField.setType(FormField.FieldType.TEXT);
            formFields.add(operatorField);

            FormField temperatureField = new FormField();
            temperatureField.setId("temperature");
            temperatureField.setLabel("温度(°C)");
            temperatureField.setName("temperature");
            temperatureField.setType(FormField.FieldType.NUMBER);
            formFields.add(temperatureField);

            FormField humidityField = new FormField();
            humidityField.setId("humidity");
            humidityField.setLabel("湿度(%)");
            humidityField.setName("humidity");
            humidityField.setType(FormField.FieldType.NUMBER);
            formFields.add(humidityField);

            FormField pressureField = new FormField();
            pressureField.setId("pressure");
            pressureField.setLabel("压力(Pa)");
            pressureField.setName("pressure");
            pressureField.setType(FormField.FieldType.NUMBER);
            formFields.add(pressureField);

            FormField remarksField = new FormField();
            remarksField.setId("remarks");
            remarksField.setLabel("备注");
            remarksField.setName("remarks");
            remarksField.setType(FormField.FieldType.TEXTAREA);
            formFields.add(remarksField);
        }
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getFormName() {
        return formName;
    }

    public void setFormName(String formName) {
        this.formName = formName;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getHeartbeatUrl() {
        return heartbeatUrl;
    }

    public void setHeartbeatUrl(String heartbeatUrl) {
        this.heartbeatUrl = heartbeatUrl;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getBackgroundImagePath() {
        return backgroundImagePath;
    }

    public void setBackgroundImagePath(String backgroundImagePath) {
        this.backgroundImagePath = backgroundImagePath;
    }

    public List<FormField> getFormFields() {
        if (formFields == null) {
            formFields = new ArrayList<>();
        }
        if (formFields.isEmpty()) {
            initializeDefaultFormFields();
        }
        return formFields;
    }

    public void setFormFields(List<FormField> formFields) {
        this.formFields = formFields;
    }

    public List<ExternalApp> getExternalApps() {
        if (externalApps == null) {
            externalApps = new ArrayList<>();
        }
        return externalApps;
    }

    public void setExternalApps(List<ExternalApp> externalApps) {
        this.externalApps = externalApps;
    }

    @Override
    public String toString() {
        return "AppConfig{" +
                "deviceId='" + deviceId + '\'' +
                ", formName='" + formName + '\'' +
                ", apiUrl='" + apiUrl + '\'' +
                ", heartbeatUrl='" + heartbeatUrl + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", backgroundImagePath='" + backgroundImagePath + '\'' +
                '}';
    }
}
