module com.logictrue {
    requires javafx.controls;
    requires javafx.fxml;
    requires org.slf4j;
    requires org.apache.httpcomponents.httpclient;
    requires org.apache.httpcomponents.httpcore;
    requires com.fasterxml.jackson.databind;
    requires com.fasterxml.jackson.core;
    requires java.desktop;

    // 导出包
    exports com.logictrue;
    exports com.logictrue.controller;
    exports com.logictrue.config;
    exports com.logictrue.service;

    // 开放包给JavaFX FXML进行反射访问
    opens com.logictrue to javafx.fxml;
    opens com.logictrue.controller to javafx.fxml;

    // 开放包给Jackson进行JSON序列化/反序列化
    opens com.logictrue.config to com.fasterxml.jackson.databind;
}
