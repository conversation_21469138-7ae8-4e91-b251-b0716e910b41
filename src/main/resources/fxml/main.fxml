<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>

<StackPane fx:id="backgroundPane" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.logictrue.controller.MainController">
   <children>
      <!-- 背景图片 -->
      <ImageView fx:id="backgroundImageView" fitHeight="600.0" fitWidth="800.0" pickOnBounds="true" preserveRatio="false" />

      <!-- 设置按钮 - 左上角 -->
      <Button fx:id="settingsButton" mnemonicParsing="false" text="⚙" StackPane.alignment="TOP_LEFT">
         <StackPane.margin>
            <Insets left="20.0" top="20.0" />
         </StackPane.margin>
      </Button>

      <!-- 快速开始按钮 - 右边垂直居中 -->
      <VBox alignment="CENTER" StackPane.alignment="CENTER_RIGHT">
         <children>
            <Button fx:id="quickStartButton" mnemonicParsing="false" text="快速开始" />
         </children>
         <StackPane.margin>
            <Insets right="80.0" />
         </StackPane.margin>
      </VBox>

      <!-- 外部应用程序按钮区域 - 底部居中 -->
      <HBox fx:id="externalAppsBox" alignment="CENTER" spacing="15.0" StackPane.alignment="BOTTOM_CENTER">
         <StackPane.margin>
            <Insets bottom="50.0" />
         </StackPane.margin>
      </HBox>
   </children>
</StackPane>
