<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>

<StackPane fx:id="backgroundPane" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.logictrue.controller.MainController">
   <children>
      <!-- 背景图片 -->
      <ImageView fx:id="backgroundImageView" fitHeight="600.0" fitWidth="800.0" pickOnBounds="true" preserveRatio="false" />

      <!-- 主要内容区域 -->
      <VBox alignment="CENTER" spacing="50.0">
         <children>
            <!-- 顶部设置按钮区域 -->
            <HBox alignment="TOP_RIGHT">
               <children>
                  <Button fx:id="settingsButton" mnemonicParsing="false" text="设置" />
               </children>
               <VBox.margin>
                  <Insets />
               </VBox.margin>
               <padding>
                  <Insets right="20.0" top="20.0" />
               </padding>
            </HBox>

            <!-- 中央按钮区域 -->
            <VBox alignment="CENTER" spacing="20.0">
               <children>
                  <!-- 快速开始按钮 -->
                  <Button fx:id="quickStartButton" mnemonicParsing="false" text="快速开始" />

                  <!-- 外部应用程序按钮区域 -->
                  <HBox fx:id="externalAppsBox" alignment="CENTER" spacing="15.0" />
               </children>
            </VBox>
         </children>
         <padding>
            <Insets bottom="50.0" left="50.0" right="50.0" top="50.0" />
         </padding>
      </VBox>
   </children>
</StackPane>
