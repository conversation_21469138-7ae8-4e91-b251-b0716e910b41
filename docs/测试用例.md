# IoT数据采集系统测试用例

## 测试环境准备

### 环境要求
- Java 11+
- Maven 3.6+
- 网络连接（用于测试网络功能）

### 测试数据准备
- 测试设备编号：`TEST_DEVICE_001`
- 测试操作员：`测试员`
- 测试心跳地址：`http://httpbin.org/status/200`
- 测试API地址：`http://httpbin.org/post`
- 测试图片地址：`http://httpbin.org/image/jpeg`

## 功能测试用例

### 1. 应用程序启动测试

#### 测试用例1.1：正常启动
**测试步骤：**
1. 执行 `mvn javafx:run` 启动应用程序
2. 观察应用程序窗口是否正常显示

**预期结果：**
- 应用程序成功启动
- 显示主界面，包含"快速开始"按钮和"设置"按钮
- 控制台输出启动成功日志
- 心跳服务自动启动

#### 测试用例1.2：配置文件不存在时启动
**测试步骤：**
1. 删除用户目录下的 `.iot-jfx` 文件夹
2. 启动应用程序

**预期结果：**
- 应用程序正常启动
- 自动创建默认配置文件
- 使用默认配置值

### 2. 主界面功能测试

#### 测试用例2.1：设置按钮功能
**测试步骤：**
1. 点击主界面右上角的"设置"按钮

**预期结果：**
- 弹出设置窗口
- 设置窗口显示所有配置项
- 加载当前配置值

#### 测试用例2.2：快速开始按钮功能
**测试步骤：**
1. 点击主界面中央的"快速开始"按钮

**预期结果：**
- 弹出表单填写窗口
- 表单标题显示配置的表单名称
- 设备编号字段预填充当前配置的设备编号

#### 测试用例2.3：背景图片显示
**测试步骤：**
1. 在设置中配置设备编号并下载图片
2. 返回主界面观察背景

**预期结果：**
- 主界面背景显示下载的设备图片
- 图片适应窗口大小

### 3. 设置界面功能测试

#### 测试用例3.1：配置项保存
**测试步骤：**
1. 打开设置界面
2. 修改各个配置项：
   - 设备编号：`TEST_DEVICE_001`
   - 表单名称：`测试数据采集表单`
   - 接口地址：`http://httpbin.org/post`
   - 心跳地址：`http://httpbin.org/status/200`
   - 图片地址：`http://httpbin.org/image/jpeg`
3. 点击"保存"按钮

**预期结果：**
- 显示"设置保存成功"提示
- 配置文件更新
- 设置窗口自动关闭

#### 测试用例3.2：设备图片下载
**测试步骤：**
1. 在设备编号字段输入：`TEST_DEVICE_001`
2. 在图片地址字段输入：`http://httpbin.org/image/jpeg`
3. 点击"下载图片"按钮

**预期结果：**
- 显示下载进度指示器
- 下载成功后显示"设备图片下载成功"提示
- 主界面背景更新为下载的图片
- 图片缓存到本地目录

#### 测试用例3.3：心跳连接测试
**测试步骤：**
1. 在心跳地址字段输入：`http://httpbin.org/status/200`
2. 点击"测试连接"按钮

**预期结果：**
- 显示测试进度指示器
- 测试成功后显示"心跳测试成功"和响应时间
- 心跳日志记录测试结果

#### 测试用例3.4：输入验证
**测试步骤：**
1. 清空表单名称字段
2. 点击"保存"按钮

**预期结果：**
- 显示"表单名称不能为空"错误提示
- 不执行保存操作

### 4. 表单填写功能测试

#### 测试用例4.1：表单数据填写和提交
**测试步骤：**
1. 打开表单填写界面
2. 填写表单数据：
   - 设备编号：`TEST_DEVICE_001`
   - 操作员：`测试员`
   - 温度：`25.5`
   - 湿度：`60.0`
   - 压力：`101325.0`
   - 备注：`测试数据提交`
3. 点击"提交"按钮

**预期结果：**
- 显示提交进度指示器
- 提交成功后显示"表单提交成功"提示
- 表单窗口自动关闭
- 应用日志记录提交结果

#### 测试用例4.2：必填字段验证
**测试步骤：**
1. 打开表单填写界面
2. 只填写设备编号，操作员字段留空
3. 点击"提交"按钮

**预期结果：**
- 显示"操作员不能为空"错误提示
- 提交按钮保持禁用状态
- 焦点移动到操作员字段

#### 测试用例4.3：数字字段验证
**测试步骤：**
1. 在温度字段输入非数字字符：`abc`

**预期结果：**
- 输入被自动过滤，只保留数字和小数点
- 字段值保持为有效的数字格式

### 5. 心跳监控功能测试

#### 测试用例5.1：自动心跳监控
**测试步骤：**
1. 配置有效的心跳地址：`http://httpbin.org/status/200`
2. 启动应用程序
3. 观察心跳日志文件

**预期结果：**
- 应用程序启动后自动开始心跳监控
- 每30秒执行一次心跳检测
- 心跳结果记录到 `logs/heartbeat.log`
- 成功的心跳显示状态码和响应时间

#### 测试用例5.2：心跳失败处理
**测试步骤：**
1. 配置无效的心跳地址：`http://invalid-url.test`
2. 观察心跳日志

**预期结果：**
- 心跳检测失败
- 日志记录失败信息和错误原因
- 应用程序继续正常运行

### 6. 异常处理测试

#### 测试用例6.1：网络连接异常
**测试步骤：**
1. 断开网络连接
2. 尝试下载设备图片
3. 尝试提交表单
4. 观察心跳监控

**预期结果：**
- 各项网络操作显示相应的错误提示
- 应用程序不崩溃
- 错误信息记录到日志文件

#### 测试用例6.2：配置文件损坏
**测试步骤：**
1. 手动修改配置文件为无效的JSON格式
2. 重启应用程序

**预期结果：**
- 应用程序正常启动
- 使用默认配置
- 日志记录配置文件加载失败信息

### 7. 性能测试

#### 测试用例7.1：界面响应性测试
**测试步骤：**
1. 执行网络请求操作（下载图片、提交表单、心跳测试）
2. 在操作进行中尝试操作界面

**预期结果：**
- 界面保持响应，不出现卡顿
- 网络操作在后台线程执行
- 进度指示器正常显示

#### 测试用例7.2：内存使用测试
**测试步骤：**
1. 长时间运行应用程序（至少1小时）
2. 多次执行各项功能操作
3. 监控内存使用情况

**预期结果：**
- 内存使用稳定，无明显泄漏
- 应用程序持续稳定运行

## 测试数据验证

### 配置文件验证
检查 `~/.iot-jfx/config.json` 文件内容：
```json
{
  "deviceId": "TEST_DEVICE_001",
  "formName": "测试数据采集表单",
  "apiUrl": "http://httpbin.org/post",
  "heartbeatUrl": "http://httpbin.org/status/200",
  "imageUrl": "http://httpbin.org/image/jpeg",
  "backgroundImagePath": "/path/to/cached/image.jpg"
}
```

### 日志文件验证
检查日志文件是否正确记录：
- `logs/application.log`：应用程序运行日志
- `logs/heartbeat.log`：心跳监控日志

### 缓存文件验证
检查缓存目录 `~/.iot-jfx/cache/` 是否包含下载的设备图片文件。

## 测试报告模板

### 测试执行记录
| 测试用例编号 | 测试用例名称 | 执行结果 | 备注 |
|-------------|-------------|----------|------|
| 1.1 | 正常启动 | ✅ 通过 | |
| 1.2 | 配置文件不存在时启动 | ✅ 通过 | |
| ... | ... | ... | ... |

### 缺陷记录
| 缺陷编号 | 缺陷描述 | 严重程度 | 状态 |
|---------|----------|----------|------|
| BUG001 | 描述 | 高/中/低 | 待修复/已修复 |

### 测试总结
- 测试用例总数：XX
- 通过用例数：XX
- 失败用例数：XX
- 发现缺陷数：XX
- 测试覆盖率：XX%
